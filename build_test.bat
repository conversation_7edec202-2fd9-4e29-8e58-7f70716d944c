@echo off
echo ========================================
echo Shellcode加载器DLL编译测试脚本
echo ========================================
echo.

echo 检查Visual Studio环境...
where cl.exe >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Visual Studio编译器
    echo 请先运行 "Developer Command Prompt for VS" 或设置环境变量
    pause
    exit /b 1
)

echo 找到编译器: 
cl.exe 2>&1 | findstr "Version"

echo.
echo 开始编译DLL...
echo.

REM 编译Debug版本
echo 编译Debug版本...
cl.exe /D_DEBUG /MDd /LD dllmain.cpp /Fe:shellcode_loader_debug.dll kernel32.lib advapi32.lib ntdll.lib user32.lib

if %errorlevel% equ 0 (
    echo Debug版本编译成功: shellcode_loader_debug.dll
) else (
    echo Debug版本编译失败
)

echo.

REM 编译Release版本
echo 编译Release版本...
cl.exe /MD /LD /O2 dllmain.cpp /Fe:shellcode_loader_release.dll kernel32.lib advapi32.lib ntdll.lib user32.lib

if %errorlevel% equ 0 (
    echo Release版本编译成功: shellcode_loader_release.dll
) else (
    echo Release版本编译失败
)

echo.
echo 编译完成！
echo.
echo 生成的文件:
dir *.dll 2>nul
echo.
echo 使用说明:
echo 1. Debug版本会显示详细调试信息
echo 2. Release版本静默运行
echo 3. 可使用DLL注入工具测试功能
echo.
pause
