# 基于"仅使用CONTEXT的无痕攻击面"技术的Shellcode加载器DLL

## 项目概述

本项目基于文章 [新型进程注入技术: 仅使用 CONTEXT 的无痕攻击面](https://cn-sec.com/archives/4075187.html) 中提到的技术，实现了一个完整的shellcode加载器DLL。

## 技术特点

### 1. 仅使用CONTEXT的无痕攻击面
- **跳过传统的分配→写入→执行模式**
- **仅使用执行原语**，避免触发EDR的内存分配和写入检测
- **利用目标进程现有内存结构**

### 2. 核心技术实现

#### 动态API调用
- 所有Windows API调用都通过动态获取函数地址的方式
- 避免静态分析和导入表检测
- 支持kernel32.dll、ntdll.dll、advapi32.dll等模块

#### ROP Gadget技术
- 自动搜索目标进程中的`push reg1; push reg2; ret`模式
- 用于修复线程栈结构
- 支持最多4个参数的函数调用

#### CreateRemoteThread + SetThreadContext注入
- 创建挂起状态的远程线程
- 使用SetThreadContext设置线程上下文
- 通过ROP gadget实现优雅的线程退出

#### 权限提升
- 自动提升SeDebugPrivilege权限
- 确保能够访问目标进程

#### 进程枚举和选择
- 支持按进程名查找目标
- Debug模式下自动选择合适的进程
- 完整的进程枚举功能

## 文件结构

```
├── dllmain.cpp          # 主要实现文件
├── build_test.bat       # 编译测试脚本
└── README.md           # 本文档
```

## 编译说明

### 环境要求
- Visual Studio 2019或更高版本
- Windows SDK
- x64平台

### 编译步骤

#### 方法1：使用批处理脚本
```batch
# 在Developer Command Prompt中运行
build_test.bat
```

#### 方法2：手动编译
```batch
# Debug版本
cl.exe /D_DEBUG /MDd /LD dllmain.cpp /Fe:shellcode_loader_debug.dll kernel32.lib advapi32.lib ntdll.lib user32.lib

# Release版本
cl.exe /MD /LD /O2 dllmain.cpp /Fe:shellcode_loader_release.dll kernel32.lib advapi32.lib ntdll.lib user32.lib
```

## 使用说明

### 1. 基本使用
- 编译生成DLL文件
- 使用DLL注入工具将DLL注入到任意进程
- DLL会自动寻找目标进程并执行shellcode注入

### 2. 目标进程配置
默认目标进程为`notepad.exe`，可在代码中修改：
```cpp
const char* targetProcessName = "your_target.exe";
```

### 3. Shellcode配置
当前使用安全的NOP测试载荷：
```cpp
BYTE g_simple_shellcode[] = {
    0x90, 0x90, 0x90, 0x90,  // nop, nop, nop, nop
    0xC3                      // ret
};
```

实际使用时可替换为所需的shellcode。

### 4. 调试模式
Debug版本特性：
- 详细的调试输出到OutputDebugString
- 错误和状态消息框提示
- 自动进程选择功能

Release版本特性：
- 静默运行
- 最小化检测特征
- 优化性能

## 安全注意事项

⚠️ **重要提醒**
- 本工具仅用于安全研究和授权测试
- 请确保在合法授权的环境中使用
- 当前使用的是无害的测试载荷
- 实际部署前请充分测试

## 技术细节

### 实现的核心功能模块

1. **InitializeFunctionPointers()** - 动态API地址获取
2. **EnableSeDebugPrivilege()** - 权限提升
3. **EnumerateProcesses()** - 进程枚举
4. **FindROPGadget()** - ROP gadget搜索
5. **ExecuteFunctionWithContext()** - 上下文执行
6. **InjectShellcode()** - 完整注入流程
7. **PerformShellcodeInjection()** - 主注入函数

### 检测规避技术

- **跳过内存分配检测** - 利用现有内存结构
- **动态API调用** - 避免静态分析
- **ROP技术** - 绕过执行流检测
- **上下文劫持** - 无需传统注入API

## 参考资料

- [新型进程注入技术: 仅使用 CONTEXT 的无痕攻击面](https://cn-sec.com/archives/4075187.html)
- [RedirectThread GitHub仓库](https://github.com/Friends-Security/RedirectThread)
- Windows x64调用约定文档
- Windows NT内核API文档

## 许可证

本项目仅用于教育和安全研究目的。使用者需自行承担使用责任。

---

**开发者**: AI Assistant  
**最后更新**: 2025-07-30
